#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python版本的KEGG能量代谢分析脚本
实现M1S2MASH, M0S4, M1S4, M1S5, M1S6的完整分析流程
"""

import argparse
import os
import sys
import logging
from pathlib import Path
import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import warnings

# 忽略一些不重要的警告
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KEGGMetabolismAnalyzer:
    """KEGG能量代谢分析器"""
    
    def __init__(self, sample_list_file, sample_dir, metabolism_file, output_dir, prefix="MASH"):
        """
        初始化分析器
        
        Args:
            sample_list_file: 样本列表文件路径
            sample_dir: 样本目录，包含各样本的KO_depth.tsv文件
            metabolism_file: KEGG能量代谢配置文件路径
            output_dir: 输出目录
            prefix: 输出文件前缀
        """
        self.sample_list_file = sample_list_file
        self.sample_dir = Path(sample_dir)
        self.metabolism_file = metabolism_file
        self.output_dir = Path(output_dir)
        self.prefix = prefix
        
        # 创建输出目录结构
        self._create_output_dirs()
        
        # 加载样本列表和代谢配置
        self.sample_list = self._load_sample_list()
        self.metabolism_info = self._load_metabolism_info()
        
    def _create_output_dirs(self):
        """创建输出目录结构"""
        dirs_to_create = [
            self.output_dir,
            self.output_dir / "AAA_KO_sum",
            self.output_dir / "tmp_KO_sum",
            self.output_dir / "M0S4_output",
            self.output_dir / "M1S4_output" / "KO_Sample_by_pathway",
            self.output_dir / "M1S4_output" / "Heatmap_by_pathway",
            self.output_dir / "M1S5_output" / "KO_Sample_by_pathway", 
            self.output_dir / "M1S5_output" / "Heatmap_by_pathway",
            self.output_dir / "M1S6_output" / "presence" / "KO_Sample_by_pathway",
            self.output_dir / "M1S6_output" / "presence" / "Heatmap_by_pathway",
            self.output_dir / "M1S6_output" / "log_transformed" / "KO_Sample_by_pathway",
            self.output_dir / "M1S6_output" / "log_transformed" / "Heatmap_by_pathway",
            self.output_dir / "M1S6_output" / "avg_transformed" / "KO_Sample_by_pathway",
            self.output_dir / "M1S6_output" / "avg_transformed" / "Heatmap_by_pathway"
        ]
        
        for dir_path in dirs_to_create:
            dir_path.mkdir(parents=True, exist_ok=True)
            
    def _load_sample_list(self):
        """加载样本列表"""
        try:
            with open(self.sample_list_file, 'r') as f:
                samples = [line.strip() for line in f if line.strip()]
            logger.info(f"Loaded {len(samples)} samples from {self.sample_list_file}")
            return samples
        except Exception as e:
            logger.error(f"Error loading sample list: {e}")
            sys.exit(1)
            
    def _load_metabolism_info(self):
        """加载KEGG能量代谢配置信息"""
        try:
            metabolism_df = pd.read_csv(self.metabolism_file, sep='\t')
            logger.info(f"Loaded metabolism info: {metabolism_df.shape}")
            return metabolism_df
        except Exception as e:
            logger.error(f"Error loading metabolism info: {e}")
            sys.exit(1)
            
    def m1s2_mash(self):
        """
        M1S2MASH: KO深度数据汇总与合并
        等价于原始的M1S2.cat_KO_depth_summary.MASH.sh
        """
        logger.info("Starting M1S2MASH: KO depth summary and merge")
        
        # 输出文件路径
        output_long = self.output_dir / "AAA_KO_sum" / f"{self.prefix}.KO_Sample.long.tsv"
        output_wide = self.output_dir / "AAA_KO_sum" / f"{self.prefix}.KO_Sample.wide.tsv"
        error_file = self.output_dir / "tmp_KO_sum" / f"{self.prefix}.error_KO_sum.tsv"
        
        # 清理已存在的文件
        for file_path in [output_long, output_wide, error_file]:
            if file_path.exists():
                file_path.unlink()
        
        # 合并所有样本的KO深度数据
        all_ko_data = []
        missing_samples = []
        
        for sample in self.sample_list:
            ko_file = self.sample_dir / f"{sample}.KO_depth.tsv"
            
            if not ko_file.exists():
                logger.warning(f"{ko_file} does not exist. Skipping {sample}...")
                missing_samples.append(sample)
                continue
                
            try:
                # 读取KO深度数据
                ko_data = pd.read_csv(ko_file, sep='\t')
                all_ko_data.append(ko_data)
                logger.info(f"Loaded KO data for sample: {sample}")
            except Exception as e:
                logger.error(f"Error reading {ko_file}: {e}")
                missing_samples.append(sample)
        
        if not all_ko_data:
            logger.error("No valid KO depth files found!")
            # 创建错误文件
            with open(error_file, 'w') as f:
                f.write("No valid KO depth files found\n")
            return None
            
        # 合并所有数据
        combined_data = pd.concat(all_ko_data, ignore_index=True)
        
        # 添加MASH_前缀（等价于sed -i "s|^|MASH_|"）
        if 'KO' in combined_data.columns:
            combined_data['KO'] = 'MASH_' + combined_data['KO'].astype(str)
        elif combined_data.columns[0]:  # 假设第一列是KO列
            combined_data.iloc[:, 0] = 'MASH_' + combined_data.iloc[:, 0].astype(str)
        
        # 保存长格式数据
        combined_data.to_csv(output_long, sep='\t', index=False)
        logger.info(f"Saved long format data to: {output_long}")
        
        # 转换为宽格式（KO为行，样本为列）
        try:
            # 假设数据格式为: KO, Sample, Depth
            if len(combined_data.columns) >= 3:
                ko_col = combined_data.columns[0]
                sample_col = combined_data.columns[1] 
                depth_col = combined_data.columns[2]
                
                wide_data = combined_data.pivot_table(
                    index=ko_col, 
                    columns=sample_col, 
                    values=depth_col, 
                    fill_value=0
                )
                
                # 重置索引，使KO成为一列
                wide_data = wide_data.reset_index()
                wide_data.columns.name = None
                
                # 保存宽格式数据
                wide_data.to_csv(output_wide, sep='\t', index=False)
                logger.info(f"Saved wide format data to: {output_wide}")
                
                return wide_data
            else:
                logger.error("Unexpected data format in KO depth files")
                return None
                
        except Exception as e:
            logger.error(f"Error converting to wide format: {e}")
            # 创建错误文件
            with open(error_file, 'w') as f:
                f.write(f"Error converting to wide format: {e}\n")
            return None
            
    def m0s4_relative_abundance(self, ko_wide_data=None):
        """
        M0S4: 相对丰度计算
        等价于原始的M0S4.Relative_Abundance.cmd.R
        """
        logger.info("Starting M0S4: Relative abundance calculation")
        
        # 如果没有提供数据，尝试从文件加载
        if ko_wide_data is None:
            wide_file = self.output_dir / "AAA_KO_sum" / f"{self.prefix}.KO_Sample.wide.tsv"
            if not wide_file.exists():
                logger.error(f"Wide format file not found: {wide_file}")
                return None
            ko_wide_data = pd.read_csv(wide_file, sep='\t', index_col=0)
        
        # 如果输入是DataFrame但没有设置索引，设置第一列为索引
        if not isinstance(ko_wide_data.index, pd.Index) or ko_wide_data.index.name is None:
            if isinstance(ko_wide_data, pd.DataFrame) and len(ko_wide_data.columns) > 0:
                ko_wide_data = ko_wide_data.set_index(ko_wide_data.columns[0])
        
        # 保存全0列和全0行的名称
        zero_cols = ko_wide_data.columns[ko_wide_data.sum(axis=0) == 0].tolist()
        zero_rows = ko_wide_data.index[ko_wide_data.sum(axis=1) == 0].tolist()
        
        # 删除全0行和全0列，并进行相对丰度计算
        sample_table = ko_wide_data.copy()
        
        # 删除全0行和列
        sample_table = sample_table.loc[sample_table.sum(axis=1) > 0, sample_table.sum(axis=0) > 0]
        
        # 转置，计算相对丰度，再转置回来
        sample_table_t = sample_table.T
        sample_table_ra = sample_table_t.div(sample_table_t.sum(axis=1), axis=0)
        sample_table_scale = sample_table_ra.T
        
        # 乘以1,000,000得到ppm单位
        sample_table_scale = sample_table_scale * 1000000
        
        # 将全0列和全0行补回来
        for col in zero_cols:
            if col not in sample_table_scale.columns:
                sample_table_scale[col] = 0
                
        for row in zero_rows:
            if row not in sample_table_scale.index:
                sample_table_scale.loc[row] = 0
        
        # 重新排序以匹配原始顺序
        sample_table_scale = sample_table_scale.reindex(
            index=ko_wide_data.index, 
            columns=ko_wide_data.columns, 
            fill_value=0
        )
        
        # 添加Orthology_Entry列并重新排列
        result_df = sample_table_scale.reset_index()
        result_df = result_df.rename(columns={result_df.columns[0]: 'Orthology_Entry'})
        
        # 保存结果
        output_file = self.output_dir / "M0S4_output" / f"{self.prefix}.KO_Sample.wide.RA.tsv"
        result_df.to_csv(output_file, sep='\t', index=False)
        logger.info(f"Saved relative abundance data to: {output_file}")
        
        return result_df

    def _group_ko_by_pathway(self, ko_data, analysis_type="M1S4"):
        """
        根据通路信息对KO进行分组

        Args:
            ko_data: KO丰度数据DataFrame
            analysis_type: 分析类型 ("M1S4", "M1S5", "M1S6")
        """
        # 确保ko_data有正确的索引
        if 'Orthology_Entry' in ko_data.columns:
            ko_data = ko_data.set_index('Orthology_Entry')

        # 获取通路信息
        pathway_groups = {}
        error_kos = []

        # 根据分析类型确定分组策略
        if analysis_type == "M1S6":
            # M1S6使用Module_Entry进行分组
            group_col = 'Module_Entry'
            name_col = 'Module_Name'
        else:
            # M1S4和M1S5使用Pathway_Entry进行分组
            group_col = 'Pathway_Entry'
            name_col = 'Pathway_Name'

        for _, row in self.metabolism_info.iterrows():
            ko_id = row['Orthology_Entry']
            group_id = row[group_col]
            group_name = row[name_col]

            # 移除MASH_前缀进行匹配
            ko_id_clean = ko_id.replace('MASH_', '') if ko_id.startswith('MASH_') else ko_id
            ko_id_with_mash = f"MASH_{ko_id_clean}"

            # 检查KO是否存在于数据中
            ko_found = False
            for ko_variant in [ko_id, ko_id_clean, ko_id_with_mash]:
                if ko_variant in ko_data.index:
                    if group_id not in pathway_groups:
                        pathway_groups[group_id] = {
                            'name': group_name,
                            'kos': [],
                            'data': []
                        }
                    pathway_groups[group_id]['kos'].append(ko_variant)
                    pathway_groups[group_id]['data'].append(ko_data.loc[ko_variant])
                    ko_found = True
                    break

            if not ko_found:
                error_kos.append(ko_id)

        return pathway_groups, error_kos

    def _aggregate_pathway_data(self, pathway_groups, method="sum"):
        """
        聚合通路数据

        Args:
            pathway_groups: 通路分组数据
            method: 聚合方法 ("sum", "mean", "max")
        """
        pathway_results = {}

        for pathway_id, pathway_info in pathway_groups.items():
            if not pathway_info['data']:
                continue

            # 将数据转换为DataFrame
            pathway_df = pd.DataFrame(pathway_info['data'])

            # 根据方法进行聚合
            if method == "sum":
                aggregated = pathway_df.sum(axis=0)
            elif method == "mean":
                aggregated = pathway_df.mean(axis=0)
            elif method == "max":
                aggregated = pathway_df.max(axis=0)
            else:
                raise ValueError(f"Unsupported aggregation method: {method}")

            pathway_results[f"{pathway_id}_{pathway_info['name']}"] = aggregated

        if pathway_results:
            result_df = pd.DataFrame(pathway_results).T
            return result_df
        else:
            return pd.DataFrame()

    def m1s4_ko_pathway_grouping(self, ko_data=None):
        """
        M1S4: KO按通路分组分析（第一种方式）
        等价于原始的M1S4.KO_Sample.group_by_pathway.sh
        """
        logger.info("Starting M1S4: KO pathway grouping analysis")

        # 加载数据
        if ko_data is None:
            ra_file = self.output_dir / "M0S4_output" / f"{self.prefix}.KO_Sample.wide.RA.tsv"
            if not ra_file.exists():
                logger.error(f"Relative abundance file not found: {ra_file}")
                return None
            ko_data = pd.read_csv(ra_file, sep='\t')

        # 按通路分组
        pathway_groups, error_kos = self._group_ko_by_pathway(ko_data, "M1S4")

        # 保存错误KO列表
        if error_kos:
            error_file = self.output_dir / "M1S4_output" / "KO_Sample_by_pathway" / f"{self.prefix}.KO_Sample.error.M1S4.tsv"
            pd.DataFrame({'Missing_KO': error_kos}).to_csv(error_file, sep='\t', index=False)
            logger.warning(f"Found {len(error_kos)} missing KOs, saved to: {error_file}")

        # 聚合通路数据
        pathway_result = self._aggregate_pathway_data(pathway_groups, method="sum")

        if not pathway_result.empty:
            # 添加Pathway_Entry列
            pathway_result = pathway_result.reset_index()
            pathway_result = pathway_result.rename(columns={'index': 'Pathway_Entry'})

            # 保存结果
            output_file = self.output_dir / "M1S4_output" / "KO_Sample_by_pathway" / f"{self.prefix}.KO_Sample.wide.M1S4.tsv"
            pathway_result.to_csv(output_file, sep='\t', index=False)
            logger.info(f"Saved M1S4 pathway grouping results to: {output_file}")

            # 生成热图
            self._generate_heatmap(pathway_result, "M1S4")

            return pathway_result
        else:
            logger.warning("No pathway data generated for M1S4")
            return None

    def m1s5_ko_pathway_grouping(self, ko_data=None):
        """
        M1S5: KO按通路分组分析（第二种方式）
        等价于原始的M1S5.KO_Sample.group_by_pathway.sh
        """
        logger.info("Starting M1S5: KO pathway grouping analysis (alternative method)")

        # 加载数据
        if ko_data is None:
            ra_file = self.output_dir / "M0S4_output" / f"{self.prefix}.KO_Sample.wide.RA.tsv"
            if not ra_file.exists():
                logger.error(f"Relative abundance file not found: {ra_file}")
                return None
            ko_data = pd.read_csv(ra_file, sep='\t')

        # 按通路分组（与M1S4相同的分组逻辑，但可能有不同的后处理）
        pathway_groups, error_kos = self._group_ko_by_pathway(ko_data, "M1S5")

        # 保存错误KO列表
        if error_kos:
            error_file = self.output_dir / "M1S5_output" / "KO_Sample_by_pathway" / f"{self.prefix}.KO_Sample.error.M1S5.tsv"
            pd.DataFrame({'Missing_KO': error_kos}).to_csv(error_file, sep='\t', index=False)
            logger.warning(f"Found {len(error_kos)} missing KOs, saved to: {error_file}")

        # 聚合通路数据（M1S5可能使用不同的聚合方法）
        pathway_result = self._aggregate_pathway_data(pathway_groups, method="mean")

        if not pathway_result.empty:
            # 添加Pathway_Entry列
            pathway_result = pathway_result.reset_index()
            pathway_result = pathway_result.rename(columns={'index': 'Pathway_Entry'})

            # 保存结果
            output_file = self.output_dir / "M1S5_output" / "KO_Sample_by_pathway" / f"{self.prefix}.KO_Sample.wide.M1S5.tsv"
            pathway_result.to_csv(output_file, sep='\t', index=False)
            logger.info(f"Saved M1S5 pathway grouping results to: {output_file}")

            # 生成热图（使用M1S5特定的热图方法）
            self._generate_heatmap(pathway_result, "M1S5")

            return pathway_result
        else:
            logger.warning("No pathway data generated for M1S5")
            return None

    def m1s6_module_pathway_grouping(self, ko_data=None):
        """
        M1S6: 模块级别的通路分组分析
        等价于原始的M1S6.Module_Sample.group_by_pathway.sh
        """
        logger.info("Starting M1S6: Module pathway grouping analysis")

        # 加载数据
        if ko_data is None:
            ra_file = self.output_dir / "M0S4_output" / f"{self.prefix}.KO_Sample.wide.RA.tsv"
            if not ra_file.exists():
                logger.error(f"Relative abundance file not found: {ra_file}")
                return None
            ko_data = pd.read_csv(ra_file, sep='\t')

        results = {}

        # 1. 存在性分析 (presence analysis)
        logger.info("Running M1S6 presence analysis...")
        presence_data = ko_data.copy()
        if 'Orthology_Entry' in presence_data.columns:
            presence_data = presence_data.set_index('Orthology_Entry')

        # 转换为存在性数据 (0/1)
        presence_data = (presence_data > 0).astype(int)
        presence_data = presence_data.reset_index()

        # 按模块分组
        pathway_groups, error_kos = self._group_ko_by_pathway(presence_data, "M1S6")

        # 使用max聚合（存在性分析）
        presence_result = self._aggregate_pathway_data(pathway_groups, method="max")

        if not presence_result.empty:
            presence_result = presence_result.reset_index()
            presence_result = presence_result.rename(columns={'index': 'Module_Entry'})

            output_file = self.output_dir / "M1S6_output" / "presence" / "KO_Sample_by_pathway" / f"{self.prefix}.KO_Sample.wide.presence.tsv"
            presence_result.to_csv(output_file, sep='\t', index=False)
            logger.info(f"Saved M1S6 presence results to: {output_file}")

            # 生成热图
            self._generate_heatmap(presence_result, "M1S6_presence")
            results['presence'] = presence_result

        # 2. 对数转换分析 (log transformed analysis)
        logger.info("Running M1S6 log transformed analysis...")
        log_data = ko_data.copy()
        if 'Orthology_Entry' in log_data.columns:
            log_data = log_data.set_index('Orthology_Entry')

        # 对数转换 (log10(x + 1))
        log_data = np.log10(log_data + 1)
        log_data = log_data.reset_index()

        # 按模块分组
        pathway_groups, _ = self._group_ko_by_pathway(log_data, "M1S6")

        # 使用sum聚合
        log_result = self._aggregate_pathway_data(pathway_groups, method="sum")

        if not log_result.empty:
            log_result = log_result.reset_index()
            log_result = log_result.rename(columns={'index': 'Module_Entry'})

            output_file = self.output_dir / "M1S6_output" / "log_transformed" / "KO_Sample_by_pathway" / f"{self.prefix}.KO_Sample.wide.log_transformed.tsv"
            log_result.to_csv(output_file, sep='\t', index=False)
            logger.info(f"Saved M1S6 log transformed results to: {output_file}")

            # 生成热图
            self._generate_heatmap(log_result, "M1S6_log_transformed")
            results['log_transformed'] = log_result

        # 3. 平均值转换分析 (average transformed analysis)
        logger.info("Running M1S6 average transformed analysis...")
        avg_data = ko_data.copy()
        if 'Orthology_Entry' in avg_data.columns:
            avg_data = avg_data.set_index('Orthology_Entry')

        avg_data = avg_data.reset_index()

        # 按模块分组
        pathway_groups, _ = self._group_ko_by_pathway(avg_data, "M1S6")

        # 使用mean聚合
        avg_result = self._aggregate_pathway_data(pathway_groups, method="mean")

        if not avg_result.empty:
            avg_result = avg_result.reset_index()
            avg_result = avg_result.rename(columns={'index': 'Module_Entry'})

            output_file = self.output_dir / "M1S6_output" / "avg_transformed" / "KO_Sample_by_pathway" / f"{self.prefix}.KO_Sample.wide.avg_transformed.tsv"
            avg_result.to_csv(output_file, sep='\t', index=False)
            logger.info(f"Saved M1S6 average transformed results to: {output_file}")

            # 生成热图
            self._generate_heatmap(avg_result, "M1S6_avg_transformed")
            results['avg_transformed'] = avg_result

        # 保存错误KO列表
        if error_kos:
            error_file = self.output_dir / "M1S6_output" / f"{self.prefix}.KO_Sample.error.M1S6.tsv"
            pd.DataFrame({'Missing_KO': error_kos}).to_csv(error_file, sep='\t', index=False)
            logger.warning(f"Found {len(error_kos)} missing KOs, saved to: {error_file}")

        return results

    def _generate_heatmap(self, data, analysis_type):
        """
        生成热图

        Args:
            data: 数据DataFrame
            analysis_type: 分析类型
        """
        try:
            # 设置图形参数
            plt.style.use('default')

            # 准备数据
            if analysis_type.startswith("M1S6"):
                index_col = 'Module_Entry'
                output_dir = self.output_dir / "M1S6_output"
                if "presence" in analysis_type:
                    output_dir = output_dir / "presence" / "Heatmap_by_pathway"
                elif "log_transformed" in analysis_type:
                    output_dir = output_dir / "log_transformed" / "Heatmap_by_pathway"
                elif "avg_transformed" in analysis_type:
                    output_dir = output_dir / "avg_transformed" / "Heatmap_by_pathway"
            else:
                index_col = 'Pathway_Entry'
                output_dir = self.output_dir / f"{analysis_type}_output" / "Heatmap_by_pathway"

            # 设置索引
            if index_col in data.columns:
                plot_data = data.set_index(index_col)
            else:
                plot_data = data.copy()

            # 只保留数值列
            numeric_cols = plot_data.select_dtypes(include=[np.number]).columns
            plot_data = plot_data[numeric_cols]

            if plot_data.empty:
                logger.warning(f"No numeric data available for {analysis_type} heatmap")
                return

            # 创建热图
            fig, ax = plt.subplots(figsize=(12, 8))

            # 使用seaborn生成热图
            sns.heatmap(plot_data,
                       cmap='viridis',
                       center=0,
                       annot=False,
                       fmt='.2f',
                       cbar_kws={'label': 'Abundance'},
                       ax=ax)

            plt.title(f'{analysis_type} Pathway Analysis Heatmap')
            plt.xlabel('Samples')
            plt.ylabel('Pathways/Modules')
            plt.xticks(rotation=45, ha='right')
            plt.yticks(rotation=0)
            plt.tight_layout()

            # 保存图片
            output_file = output_dir / f"{self.prefix}.KO_Sample.wide.{analysis_type}.png"
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            plt.close()

            logger.info(f"Saved {analysis_type} heatmap to: {output_file}")

        except Exception as e:
            logger.error(f"Error generating heatmap for {analysis_type}: {e}")
            plt.close()

    def run_complete_analysis(self):
        """
        运行完整的分析流程
        """
        logger.info("Starting complete KEGG metabolism analysis pipeline")

        try:
            # Step 1: M1S2MASH - KO深度数据汇总与合并
            logger.info("=" * 60)
            logger.info("Step 1: M1S2MASH - KO depth summary and merge")
            logger.info("=" * 60)
            ko_wide_data = self.m1s2_mash()

            if ko_wide_data is None:
                logger.error("M1S2MASH failed, stopping analysis")
                return False

            # Step 2: M0S4 - 相对丰度计算
            logger.info("=" * 60)
            logger.info("Step 2: M0S4 - Relative abundance calculation")
            logger.info("=" * 60)
            ra_data = self.m0s4_relative_abundance(ko_wide_data)

            if ra_data is None:
                logger.error("M0S4 failed, stopping analysis")
                return False

            # Step 3: M1S4 - KO按通路分组分析（第一种方式）
            logger.info("=" * 60)
            logger.info("Step 3: M1S4 - KO pathway grouping analysis (method 1)")
            logger.info("=" * 60)
            m1s4_result = self.m1s4_ko_pathway_grouping(ra_data)

            # Step 4: M1S5 - KO按通路分组分析（第二种方式）
            logger.info("=" * 60)
            logger.info("Step 4: M1S5 - KO pathway grouping analysis (method 2)")
            logger.info("=" * 60)
            m1s5_result = self.m1s5_ko_pathway_grouping(ra_data)

            # Step 5: M1S6 - 模块级别的通路分组分析
            logger.info("=" * 60)
            logger.info("Step 5: M1S6 - Module pathway grouping analysis")
            logger.info("=" * 60)
            m1s6_results = self.m1s6_module_pathway_grouping(ra_data)

            # 生成分析报告
            self._generate_analysis_report(ko_wide_data, ra_data, m1s4_result, m1s5_result, m1s6_results)

            logger.info("=" * 60)
            logger.info("KEGG METABOLISM ANALYSIS PIPELINE SUCCESSFULLY FINISHED!")
            logger.info("=" * 60)

            return True

        except Exception as e:
            logger.error(f"Analysis pipeline failed: {e}")
            return False

    def _generate_analysis_report(self, ko_wide_data, ra_data, m1s4_result, m1s5_result, m1s6_results):
        """
        生成分析报告
        """
        try:
            report_file = self.output_dir / f"{self.prefix}_analysis_report.txt"

            with open(report_file, 'w') as f:
                f.write("KEGG Metabolism Analysis Report\n")
                f.write("=" * 50 + "\n\n")

                f.write(f"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Prefix: {self.prefix}\n")
                f.write(f"Number of samples: {len(self.sample_list)}\n")
                f.write(f"Metabolism config file: {self.metabolism_file}\n\n")

                # M1S2MASH结果
                if ko_wide_data is not None:
                    f.write("M1S2MASH Results:\n")
                    f.write(f"  - Total KO entries: {len(ko_wide_data)}\n")
                    f.write(f"  - Total samples: {len(ko_wide_data.columns) - 1}\n\n")

                # M0S4结果
                if ra_data is not None:
                    f.write("M0S4 Results:\n")
                    f.write(f"  - Relative abundance matrix shape: {ra_data.shape}\n\n")

                # M1S4结果
                if m1s4_result is not None:
                    f.write("M1S4 Results:\n")
                    f.write(f"  - Pathway groups: {len(m1s4_result)}\n\n")

                # M1S5结果
                if m1s5_result is not None:
                    f.write("M1S5 Results:\n")
                    f.write(f"  - Pathway groups: {len(m1s5_result)}\n\n")

                # M1S6结果
                if m1s6_results:
                    f.write("M1S6 Results:\n")
                    for analysis_type, result in m1s6_results.items():
                        if result is not None:
                            f.write(f"  - {analysis_type}: {len(result)} module groups\n")
                    f.write("\n")

                f.write("Output Files:\n")
                f.write(f"  - Main output directory: {self.output_dir}\n")
                f.write(f"  - KO summary: AAA_KO_sum/\n")
                f.write(f"  - Relative abundance: M0S4_output/\n")
                f.write(f"  - M1S4 results: M1S4_output/\n")
                f.write(f"  - M1S5 results: M1S5_output/\n")
                f.write(f"  - M1S6 results: M1S6_output/\n")

            logger.info(f"Analysis report saved to: {report_file}")

        except Exception as e:
            logger.error(f"Error generating analysis report: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='Python版本的KEGG能量代谢分析脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python kegg_metabolism_analysis.py \\
    --sample_list samples.txt \\
    --sample_dir /path/to/ko_depth_files \\
    --metabolism_file MASH_KEGG_Energy_metabolism.tsv \\
    --output_dir /path/to/output \\
    --prefix MASH_20240101

输入文件格式:
  - sample_list: 每行一个样本名称
  - sample_dir: 包含{sample}.KO_depth.tsv文件的目录
  - metabolism_file: KEGG能量代谢配置文件(TSV格式)
        """
    )

    parser.add_argument('--sample_list', required=True,
                       help='样本列表文件路径')
    parser.add_argument('--sample_dir', required=True,
                       help='样本目录，包含各样本的KO_depth.tsv文件')
    parser.add_argument('--metabolism_file', required=True,
                       help='KEGG能量代谢配置文件路径')
    parser.add_argument('--output_dir', required=True,
                       help='输出目录')
    parser.add_argument('--prefix', default='MASH',
                       help='输出文件前缀 (默认: MASH)')
    parser.add_argument('--verbose', action='store_true',
                       help='显示详细日志信息')

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 验证输入文件
    if not os.path.exists(args.sample_list):
        logger.error(f"Sample list file not found: {args.sample_list}")
        sys.exit(1)

    if not os.path.exists(args.sample_dir):
        logger.error(f"Sample directory not found: {args.sample_dir}")
        sys.exit(1)

    if not os.path.exists(args.metabolism_file):
        logger.error(f"Metabolism file not found: {args.metabolism_file}")
        sys.exit(1)

    # 创建分析器并运行分析
    try:
        analyzer = KEGGMetabolismAnalyzer(
            sample_list_file=args.sample_list,
            sample_dir=args.sample_dir,
            metabolism_file=args.metabolism_file,
            output_dir=args.output_dir,
            prefix=args.prefix
        )

        success = analyzer.run_complete_analysis()

        if success:
            logger.info("Analysis completed successfully!")
            sys.exit(0)
        else:
            logger.error("Analysis failed!")
            sys.exit(1)

    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()

"""
使用示例:

1. 准备输入文件:
   - sample_list.txt: 样本列表文件
     sample1
     sample2
     sample3

   - sample_dir/: 包含KO深度文件的目录
     sample1.KO_depth.tsv
     sample2.KO_depth.tsv
     sample3.KO_depth.tsv

   - MASH_KEGG_Energy_metabolism.tsv: KEGG能量代谢配置文件

2. 运行分析:
   python kegg_metabolism_analysis.py \
     --sample_list sample_list.txt \
     --sample_dir /path/to/ko_depth_files \
     --metabolism_file MASH_KEGG_Energy_metabolism.tsv \
     --output_dir /path/to/output \
     --prefix MASH_20240101

3. 输出结果:
   output_dir/
   ├── AAA_KO_sum/                    # M1S2MASH输出
   │   ├── MASH_20240101.KO_Sample.long.tsv
   │   └── MASH_20240101.KO_Sample.wide.tsv
   ├── M0S4_output/                   # M0S4相对丰度输出
   │   └── MASH_20240101.KO_Sample.wide.RA.tsv
   ├── M1S4_output/                   # M1S4通路分组输出
   │   ├── KO_Sample_by_pathway/
   │   └── Heatmap_by_pathway/
   ├── M1S5_output/                   # M1S5通路分组输出
   │   ├── KO_Sample_by_pathway/
   │   └── Heatmap_by_pathway/
   ├── M1S6_output/                   # M1S6模块分组输出
   │   ├── presence/
   │   ├── log_transformed/
   │   └── avg_transformed/
   └── MASH_20240101_analysis_report.txt
"""
