# KEGG能量代谢分析脚本 (Python版本)

这是一个Python版本的KEGG能量代谢分析脚本，实现了与原始Shell/R脚本相同的功能，包括M1S2MASH、M0S4、M1S4、M1S5、M1S6的完整分析流程。

## 功能概述

1. **M1S2MASH**: KO深度数据汇总与合并
2. **M0S4**: 相对丰度计算
3. **M1S4**: KO按通路分组分析（方法1）
4. **M1S5**: KO按通路分组分析（方法2）
5. **M1S6**: 模块级别的通路分组分析（包含存在性、对数转换、平均值转换三种分析）

## 依赖包

```bash
pip install pandas numpy seaborn matplotlib
```

## 输入文件要求

### 1. 样本列表文件 (sample_list.txt)
每行一个样本名称：
```
sample1
sample2
sample3
```

### 2. 样本目录 (sample_dir/)
包含各样本的KO深度文件，文件名格式为 `{sample}.KO_depth.tsv`：
```
sample_dir/
├── sample1.KO_depth.tsv
├── sample2.KO_depth.tsv
└── sample3.KO_depth.tsv
```

KO深度文件格式（TSV）：
```
KO	Sample	Depth
K00001	sample1	100.5
K00002	sample1	200.3
...
```

### 3. KEGG能量代谢配置文件
TSV格式，包含以下列：
- Orthology_Entry: KO编号
- Module_Entry: 模块编号
- Pathway_Entry: 通路编号
- Pathway_Name: 通路名称
- Module_Name: 模块名称
- 其他相关列...

## 使用方法

### 基本用法
```bash
python kegg_metabolism_analysis.py \
  --sample_list sample_list.txt \
  --sample_dir /path/to/ko_depth_files \
  --metabolism_file MASH_KEGG_Energy_metabolism.tsv \
  --output_dir /path/to/output \
  --prefix MASH_20240101
```

### 参数说明
- `--sample_list`: 样本列表文件路径
- `--sample_dir`: 包含KO深度文件的目录
- `--metabolism_file`: KEGG能量代谢配置文件路径
- `--output_dir`: 输出目录
- `--prefix`: 输出文件前缀（默认：MASH）
- `--verbose`: 显示详细日志信息

## 输出结果

```
output_dir/
├── AAA_KO_sum/                           # M1S2MASH输出
│   ├── {prefix}.KO_Sample.long.tsv       # 长格式KO-样本矩阵
│   └── {prefix}.KO_Sample.wide.tsv       # 宽格式KO-样本矩阵
├── tmp_KO_sum/                           # 临时文件
│   └── {prefix}.error_KO_sum.tsv         # 错误日志
├── M0S4_output/                          # M0S4相对丰度输出
│   └── {prefix}.KO_Sample.wide.RA.tsv    # 相对丰度矩阵
├── M1S4_output/                          # M1S4通路分组输出
│   ├── KO_Sample_by_pathway/
│   │   └── {prefix}.KO_Sample.wide.M1S4.tsv
│   └── Heatmap_by_pathway/
│       └── {prefix}.KO_Sample.wide.M1S4.png
├── M1S5_output/                          # M1S5通路分组输出
│   ├── KO_Sample_by_pathway/
│   │   └── {prefix}.KO_Sample.wide.M1S5.tsv
│   └── Heatmap_by_pathway/
│       └── {prefix}.KO_Sample.wide.M1S5.png
├── M1S6_output/                          # M1S6模块分组输出
│   ├── presence/                         # 存在性分析
│   │   ├── KO_Sample_by_pathway/
│   │   └── Heatmap_by_pathway/
│   ├── log_transformed/                  # 对数转换分析
│   │   ├── KO_Sample_by_pathway/
│   │   └── Heatmap_by_pathway/
│   └── avg_transformed/                  # 平均值转换分析
│       ├── KO_Sample_by_pathway/
│       └── Heatmap_by_pathway/
└── {prefix}_analysis_report.txt          # 分析报告
```

## 分析流程说明

1. **数据合并**: 将所有样本的KO深度数据合并为统一的矩阵
2. **相对丰度计算**: 将绝对丰度转换为相对丰度（ppm单位）
3. **通路分组**: 根据KEGG通路信息对KO进行分组聚合
4. **模块分析**: 在模块级别进行多种类型的分析
5. **可视化**: 生成热图展示分析结果

## 注意事项

1. 确保所有输入文件格式正确
2. 样本名称在样本列表和文件名中必须一致
3. KO深度文件必须包含正确的列名
4. 建议在运行前检查输入文件的完整性

## 错误处理

脚本会自动处理以下情况：
- 缺失的样本文件
- 格式错误的数据文件
- 不匹配的KO条目
- 空的数据集

所有错误信息会记录在日志中，并生成相应的错误文件。
